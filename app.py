from flask import Flask, render_template, request, jsonify, send_file, send_from_directory
import os
import json
import uuid
import threading
import time
from datetime import datetime
import data_scrape_core as core
import pathlib
import shutil
import subprocess
import platform
import webbrowser

app = Flask(__name__)

# Thông tin đăng nhập mặc định
DEFAULT_USERNAME = "princekiix"
DEFAULT_PASSWORD = "Beyondk@2025"

# Lưu trữ thông tin các tác vụ đang chạy
tasks = {}
task_lock = threading.Lock()

# Đường dẫn lưu trữ tạm thời
TEMP_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
os.makedirs(TEMP_DIR, exist_ok=True)

# Danh sách các thư mục thông dụng
COMMON_FOLDERS = {
    'temp': TEMP_DIR,
    'desktop': os.path.join(os.path.expanduser('~'), 'Desktop'),
    'documents': os.path.join(os.path.expanduser('~'), 'Documents'),
    'downloads': os.path.join(os.path.expanduser('~'), 'Downloads')
}

# Danh sách ổ đĩa (chỉ cho Windows)
def get_drives():
    if platform.system() == 'Windows':
        drives = []
        for letter in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ':
            drive = f"{letter}:\\"
            if os.path.exists(drive):
                drives.append(drive)
        return drives
    return []

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    username = data.get('username', DEFAULT_USERNAME)
    password = data.get('password', DEFAULT_PASSWORD)

    try:
        # Gọi hàm đăng nhập từ core để lấy cookies
        core.get_logged_in_cookies(username, password, headless=True)
        # Kiểm tra xem headers có được tạo không
        headers = core.get_auth_headers()
        if headers:
            return jsonify({'success': True, 'message': 'Đăng nhập thành công'})
        else:
            return jsonify({'success': False, 'message': 'Đăng nhập thất bại: Không nhận được headers'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Đăng nhập thất bại: {str(e)}'})

@app.route('/api/list_folders', methods=['POST'])
def list_folders():
    data = request.json
    path = data.get('path', '')

    # Nếu đường dẫn trống, trả về danh sách ổ đĩa (Windows) hoặc thư mục gốc (Unix)
    if not path:
        if platform.system() == 'Windows':
            drives = get_drives()
            return jsonify({
                'success': True,
                'current_path': '',
                'parent_path': None,
                'folders': [{'name': drive, 'path': drive} for drive in drives]
            })
        else:
            path = '/'

    # Xử lý đường dẫn để đảm bảo tính bảo mật
    try:
        path = process_output_path(path)

        # Kiểm tra nếu đường dẫn không tồn tại
        if not os.path.exists(path):
            return jsonify({'success': False, 'message': 'Đường dẫn không tồn tại'})

        # Lấy đường dẫn cha
        parent_path = os.path.dirname(path)
        if path == parent_path:  # Trường hợp ở thư mục gốc
            parent_path = None

        # Danh sách các thư mục con
        folders = []
        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                folders.append({
                    'name': item,
                    'path': item_path
                })

        # Sắp xếp theo tên
        folders.sort(key=lambda x: x['name'].lower())

        return jsonify({
            'success': True,
            'current_path': path,
            'parent_path': parent_path,
            'folders': folders
        })

    except PermissionError:
        return jsonify({'success': False, 'message': 'Không có quyền truy cập thư mục này'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/api/check_path', methods=['POST'])
def check_path():
    data = request.json
    path = data.get('path', '')

    if not path:
        return jsonify({'success': False, 'message': 'Đường dẫn không được để trống'})

    try:
        # Xử lý đường dẫn để đảm bảo tính bảo mật
        path = process_output_path(path)

        # Kiểm tra đường dẫn có tồn tại không
        if os.path.exists(path):
            if os.path.isdir(path):
                # Kiểm tra quyền ghi
                test_file = os.path.join(path, '.test_write_permission')
                try:
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)
                    return jsonify({'success': True, 'message': 'Đường dẫn hợp lệ và có quyền ghi', 'path': path, 'exists': True, 'writable': True})
                except:
                    return jsonify({'success': True, 'message': 'Đường dẫn tồn tại nhưng không có quyền ghi', 'path': path, 'exists': True, 'writable': False})
            else:
                return jsonify({'success': False, 'message': 'Đường dẫn là một file, không phải thư mục'})
        else:
            # Thử tạo thư mục
            try:
                os.makedirs(path, exist_ok=True)
                # Kiểm tra quyền ghi
                test_file = os.path.join(path, '.test_write_permission')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                return jsonify({'success': True, 'message': 'Đã tạo thư mục thành công', 'path': path, 'exists': True, 'writable': True})
            except:
                return jsonify({'success': False, 'message': 'Không thể tạo thư mục'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi: {str(e)}'})

@app.route('/api/open_folder', methods=['POST'])
def open_folder():
    data = request.json
    path = data.get('path', '')

    if not path or not os.path.exists(path):
        return jsonify({'success': False, 'message': 'Đường dẫn không tồn tại'})

    try:
        if platform.system() == "Windows":
            os.startfile(path)
        elif platform.system() == "Darwin":  # macOS
            subprocess.call(["open", path])
        else:  # Linux
            subprocess.call(["xdg-open", path])
        return jsonify({'success': True, 'message': 'Đã mở thư mục'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Không thể mở thư mục: {str(e)}'})

@app.route('/api/start_scraping', methods=['POST'])
def start_scraping():
    data = request.json
    shop_ids = data.get('shop_ids', [])
    output_path = data.get('output_path', '')
    output_filename = data.get('output_filename', f'scraped_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
    username = data.get('username', DEFAULT_USERNAME)
    password = data.get('password', DEFAULT_PASSWORD)

    if not shop_ids:
        return jsonify({'success': False, 'message': 'Vui lòng cung cấp ít nhất một ID shop'})

    # Tạo task ID và lưu trữ thông tin
    task_id = str(uuid.uuid4())

    # Xử lý đường dẫn lưu trữ
    if not output_path:
        # Nếu không chọn đường dẫn, sử dụng thư mục temp
        output_path = TEMP_DIR
    else:
        output_path = process_output_path(output_path)

    # Đảm bảo output_path tồn tại
    try:
        os.makedirs(output_path, exist_ok=True)
    except Exception as e:
        return jsonify({'success': False, 'message': f'Không thể tạo thư mục: {str(e)}'})

    # Đường dẫn đầy đủ cho file output
    full_output_path = os.path.join(output_path, output_filename)
    if not full_output_path.endswith('.xlsx'):
        full_output_path += '.xlsx'

    # Khởi tạo thông tin tác vụ
    with task_lock:
        tasks[task_id] = {
            'status': 'running',
            'start_time': time.time(),
            'shop_ids': shop_ids,
            'total_shops': len(shop_ids),
            'completed_shops': 0,
            'total_products': 0,
            'output_file': full_output_path,
            'output_folder': output_path,
            'log_messages': [],
            'shop_statuses': {shop_id: {'status': 'waiting', 'product_count': 0} for shop_id in shop_ids}
        }

    # Bắt đầu thread để xử lý dữ liệu
    thread = threading.Thread(
        target=process_data,
        args=(task_id, username, password, shop_ids, output_path, output_filename)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': f'Đã bắt đầu xử lý {len(shop_ids)} shop'
    })

def process_output_path(path):
    """Xử lý đường dẫn đầu ra được chọn từ giao diện"""
    # Nếu là thư mục trong danh sách thông dụng
    if path.lower() in COMMON_FOLDERS:
        return COMMON_FOLDERS[path.lower()]

    # Nếu là đường dẫn tương đối, chuyển thành đường dẫn tuyệt đối
    if not os.path.isabs(path):
        path = os.path.join(os.path.dirname(os.path.abspath(__file__)), path)

    # Mở rộng ~ nếu có (home directory)
    path = os.path.expanduser(path)

    # Đảm bảo đường dẫn hợp lệ
    if os.name == 'nt':  # Windows
        # Xử lý ổ đĩa cho Windows
        if path.startswith('/') and len(path) > 2 and path[2] == ':':
            # Chuyển /C:/path thành C:/path
            path = path[1:]
        elif not path[1] == ':':
            # Nếu không có chỉ định ổ đĩa, thêm ổ đĩa hiện tại
            current_drive = os.getcwd()[:2]
            if not path.startswith('/'):
                path = os.path.join(current_drive, path)
            else:
                path = current_drive + path

    # Xử lý các dấu cách đường dẫn
    path = os.path.normpath(path)

    return path

def process_data(task_id, username, password, shop_ids, output_path, output_filename):
    """Hàm xử lý dữ liệu trong thread riêng biệt"""

    def log_callback(message):
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['log_messages'].append(message)

                # Phân tích log message để xác định shop_id và cập nhật trạng thái
                import re
                shop_id_match = re.search(r"Đang lấy dữ liệu từ shop ID: (\d+)", message)
                completed_match = re.search(r"Shop ID (\d+): Hoàn thành với (\d+) sản phẩm", message)
                no_products_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào", message)
                no_products_retry_match = re.search(r"Shop ID (\d+): Không có sản phẩm nào \(đã retry (\d+) lần\)", message)
                error_match = re.search(r"Lỗi khi xử lý shop ID (\d+)", message)

                if shop_id_match:
                    shop_id = shop_id_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'processing'
                elif completed_match:
                    shop_id = completed_match.group(1)
                    product_count = int(completed_match.group(2))
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'completed'
                    tasks[task_id]['shop_statuses'][shop_id]['product_count'] = product_count
                    tasks[task_id]['completed_shops'] += 1
                    tasks[task_id]['total_products'] += product_count
                elif no_products_match or no_products_retry_match:
                    if no_products_match:
                        shop_id = no_products_match.group(1)
                    else:
                        shop_id = no_products_retry_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'no_products'
                    tasks[task_id]['completed_shops'] += 1
                elif error_match:
                    shop_id = error_match.group(1)
                    tasks[task_id]['shop_statuses'][shop_id]['status'] = 'error'
                    tasks[task_id]['completed_shops'] += 1

    try:
        full_output_path = os.path.join(output_path, output_filename)
        if not full_output_path.endswith('.xlsx'):
            full_output_path += '.xlsx'

        # Gọi hàm xử lý từ core
        result = core.fetch_and_save_multiple_shops(
            username,
            password,
            shop_ids,
            output_filename=full_output_path,
            headless=True,
            timeout=30,
            max_retries=3,
            log_callback=log_callback,
            max_workers=8  # Sử dụng 8 luồng đồng thời như trong PyQt app
        )

        # Cập nhật trạng thái tác vụ
        with task_lock:
            if task_id in tasks:
                if result:
                    tasks[task_id]['status'] = 'completed'
                    tasks[task_id]['log_messages'].append(f"📊 Tổng cộng: {tasks[task_id]['total_products']} sản phẩm được lưu vào file Excel")
                else:
                    tasks[task_id]['status'] = 'error'
                    tasks[task_id]['log_messages'].append("❌ Xử lý dữ liệu thất bại")

    except Exception as e:
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'error'
                tasks[task_id]['log_messages'].append(f"❌ Lỗi: {str(e)}")

@app.route('/api/get_task_status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    with task_lock:
        if task_id not in tasks:
            return jsonify({'success': False, 'message': 'Không tìm thấy tác vụ'})

        task = tasks[task_id].copy()

        # Tính thời gian đã chạy
        elapsed_time = time.time() - task['start_time']
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        task['elapsed_time'] = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

        # Tính phần trăm hoàn thành
        if task['total_shops'] > 0:
            task['progress_percent'] = int((task['completed_shops'] / task['total_shops']) * 100)
        else:
            task['progress_percent'] = 0

        return jsonify({'success': True, 'task': task})

@app.route('/api/get_file/<task_id>', methods=['GET'])
def get_file(task_id):
    with task_lock:
        if task_id not in tasks or 'output_file' not in tasks[task_id]:
            return jsonify({'success': False, 'message': 'Không tìm thấy file'})

        output_file = tasks[task_id]['output_file']

    if not os.path.exists(output_file):
        return jsonify({'success': False, 'message': 'File không tồn tại'})

    return send_file(output_file, as_attachment=True)

@app.route('/api/convert_csv', methods=['POST'])
def convert_csv():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'Không tìm thấy file'})

    csv_file = request.files['file']

    if csv_file.filename == '':
        return jsonify({'success': False, 'message': 'Không có file nào được chọn'})

    if not csv_file.filename.endswith('.csv'):
        return jsonify({'success': False, 'message': 'Chỉ chấp nhận file CSV'})

    # Tạo đường dẫn lưu file tạm thời
    csv_temp_path = os.path.join(TEMP_DIR, f"temp_{uuid.uuid4()}.csv")
    excel_temp_path = csv_temp_path.replace('.csv', '.xlsx')

    # Lưu file CSV
    csv_file.save(csv_temp_path)

    # Tạo task ID
    task_id = str(uuid.uuid4())
    with task_lock:
        tasks[task_id] = {
            'status': 'running',
            'start_time': time.time(),
            'output_file': excel_temp_path,
            'output_folder': os.path.dirname(excel_temp_path),
            'log_messages': ["🔄 Bắt đầu chuyển đổi CSV thành Excel..."]
        }

    # Bắt đầu thread để xử lý chuyển đổi
    thread = threading.Thread(
        target=process_csv_conversion,
        args=(task_id, csv_temp_path, excel_temp_path)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': 'Đã bắt đầu chuyển đổi CSV thành Excel'
    })

def process_csv_conversion(task_id, csv_path, excel_path):
    """Hàm xử lý chuyển đổi CSV thành Excel trong thread riêng biệt"""
    try:
        import pandas as pd
        from openpyxl import Workbook
        from openpyxl.styles import Font

        # Đọc CSV
        df = pd.read_csv(csv_path)

        # Format theo yêu cầu
        key_col = df['id'].astype(str) + '_' + df['shopID'].astype(str)
        sku_col = df['id']
        id_col = df['id']

        new_df = pd.DataFrame({
            '': key_col,
            ' ': sku_col,
            '  ': id_col
        })

        # Thêm các cột còn lại
        column_mapping = {
            'linkProduct': 'Link sản phẩm',
            'linkShop': 'Link Shop',
            'name': 'Tên sản phẩm',
            'brand': 'Thương hiệu',
            'description': 'Mô tả',
            'timeCreate': 'Ngày tạo',
            'itemID': 'Mã Shop',
            'shopID': 'Mã Sản phẩm',
            'categoryMain': 'Chuyên mục',
            'categoryTree': 'Chuyên mục.1',
            'price': 'Giá hiện tại',
            'priceMin': 'Giá thấp nhất',
            'priceMax': 'Giá cao nhất',
            'discount': 'Giảm giá',
            'stock': 'Tồn kho',
            'weight': 'Cân nặng',
            'image': 'Hình ảnh',
            'cmtCount': 'Số Đánh giá',
            'viewCount': 'Số lượt xem',
            'likedCount': 'Số thích',
            'itemRating': 'Điểm đánh giá',
            'sold_30day': 'Đã bán 30 ngày',
            'sale_30day': 'Doanh số 30 ngày',
            'sold_alltime': 'Đã bán toàn thời gian',
            'sale_alltime': 'Doanh số toàn thời gian',
            'location': 'Vị trí',
            'video': 'Video'
        }

        for col_name, new_name in column_mapping.items():
            if col_name in df.columns:
                new_df[new_name] = df[col_name]

        # Tạo Excel
        wb = Workbook()
        ws = wb.active

        # Ghi headers
        for c_idx, column in enumerate(new_df.columns, 1):
            if c_idx <= 3:
                header_value = ""  # 3 cột đầu trống
            else:
                header_value = column
            ws.cell(row=1, column=c_idx, value=header_value)
            ws.cell(row=1, column=c_idx).font = Font(bold=False)

        # Ghi dữ liệu từ row 2
        for r_idx, (_, row) in enumerate(new_df.iterrows(), 2):
            for c_idx, value in enumerate(row, 1):
                try:
                    ws.cell(row=r_idx, column=c_idx, value=value)
                except:
                    ws.cell(row=r_idx, column=c_idx, value="[Lỗi dữ liệu]")

        # Lưu file Excel
        wb.save(excel_path)

        # Cập nhật trạng thái
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'completed'
                tasks[task_id]['log_messages'].append("✅ Chuyển đổi thành công!")
                tasks[task_id]['log_messages'].append(f"📊 Đã xử lý {len(df)} dòng dữ liệu")

        # Xóa file CSV tạm thời
        try:
            os.remove(csv_path)
        except:
            pass

    except Exception as e:
        with task_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = 'error'
                tasks[task_id]['log_messages'].append(f"❌ Lỗi: {str(e)}")

@app.route('/api/get_default_credentials', methods=['GET'])
def get_default_credentials():
    return jsonify({
        'username': DEFAULT_USERNAME,
        'password': DEFAULT_PASSWORD
    })

@app.route('/api/update_credentials', methods=['POST'])
def update_credentials():
    data = request.json
    username = data.get('username')
    password = data.get('password')

    if not username or not password:
        return jsonify({'success': False, 'message': 'Username và password không được để trống'})

    global DEFAULT_USERNAME, DEFAULT_PASSWORD
    DEFAULT_USERNAME = username
    DEFAULT_PASSWORD = password

    # Cập nhật thông tin đăng nhập và force refresh
    try:
        core.get_logged_in_cookies(username, password, headless=True, force_refresh=True)
        return jsonify({'success': True, 'message': 'Đã cập nhật thông tin đăng nhập'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Lỗi khi cập nhật thông tin đăng nhập: {str(e)}'})

def open_browser():
    """Mở trình duyệt sau khi server đã khởi động"""
    time.sleep(1.5)  # Đợi server khởi động hoàn tất
    webbrowser.open('http://127.0.0.1:5000')

if __name__ == '__main__':
    # Khởi động thread để mở trình duyệt
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

    # Khởi động Flask server
    print("🚀 Đang khởi động server...")
    print("🌐 Server sẽ chạy tại: http://127.0.0.1:5000")
    print("🔗 Trình duyệt sẽ tự động mở trong giây lát...")
    app.run(debug=True, use_reloader=False)